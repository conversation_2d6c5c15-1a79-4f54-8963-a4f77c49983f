import 'dotenv/config';
import { AgentFactory, TeamFactory, AgencyFactory } from '@virtron/agency';
import { webSearchTool } from '@virtron/agency-tools';


async function main() {
  // 1. Create and configure the AgentFactory and TeamFactory
  const agentFactory = new AgentFactory({
    apiKeys: {
      gemini: process.env.GEMINI_API_KEY,
    }
  });

  // Register the webSearchTool with the AgentFactory, as several agents use it
  agentFactory.registerTool(webSearchTool);

  const teamFactory = new TeamFactory({ agentFactory });

  // 2. We will use the AgencyFactory to load the entire configuration
  // This is the correct way to load a configuration file that contains
  // definitions for agents, teams, and briefs.
  const agencyFactory = new AgencyFactory({ agentFactory, teamFactory });

  // Load the full agency configuration from the external JSON file.
  // The public method 'loadAgencyFromFile' should be used.
  let agency;
  try {
    agency = await agencyFactory.loadAgencyFromFile('./vp.json');
  } catch (error) {
    console.error('Failed to load agency configuration:', error);
    process.exit(1);
  }

  // 3. Get the team and brief from the loaded agency instance.
  // The AgencyFactory has already created and stored them for us.
  const teamId = 'vacationTeam';
  const briefId = 'st-pete-clearwater-trip-001';

  const team = agencyFactory.getTeam(teamId);
  const initialBrief = agencyFactory.getBrief(briefId);
  if (!team) {
    console.error(`Team with ID "${teamId}" not found in configuration.`);
    process.exit(1);
  }
  if (!initialBrief) {
    console.error(`Brief with ID "${briefId}" not found in configuration.`);
    process.exit(1);
  }

  // 4. Run the team with the initial brief
  console.log(`Running team "${team.name}" with brief for "${briefId}"`);

  try {
    const finalResult = await team.run(initialBrief);
    console.log('Team Final Result:');
    console.log(finalResult);
  } catch (error) {
    console.error('An error occurred while running the team:', error);
  }
}

main();
