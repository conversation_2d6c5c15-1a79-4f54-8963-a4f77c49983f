import 'dotenv/config';
import { AgentFactory } from '@virtron/agency';
import { webSearchTool } from '@virtron/agency-tools';

async function main() {
  // 1. Create and configure the AgentFactory
  const factory = new AgentFactory({
    apiKeys: {
      gemini: process.env.GEMINI_API_KEY,
    }
  });

  // Register only the web search tool with the AgentFactory
  factory.registerTool(webSearchTool);

  // 2. Define the configuration for the research agent
  const agentConfig = {
    id: 'research-agent',
    name: 'Research Agent',
    description: 'An agent that uses the Gemini API and can find information online.',
    provider: 'gemini',
    llmConfig: {
      model: 'gemini-2.5-flash-lite', 
    },
    role: 'A helpful assistant that specializes in finding information using web searches.',
    goals: [
      'Use the web search tool to find information to answer user questions.',
      'Provide concise and accurate summaries of the information found.'
    ],
    tools: {
      webSearch: 'webSearch',
    },
  };

  // 3. Create the agent
  const agent = factory.createAgent(agentConfig);

  // 4. Run the agent with a prompt that requires a web search
  const prompt = 'Who is the current Prime Minister of the United Kingdom?';
  console.log(`Running agent with prompt: "${prompt}"`);

  try {
    const response = await agent.run(prompt);
    console.log('Agent Response:');
    console.log(response);
  } catch (error) {
    console.error('An error occurred while running the agent:', error);
  }
}

main();
