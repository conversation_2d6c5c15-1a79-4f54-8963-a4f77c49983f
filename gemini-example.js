import 'dotenv/config';
import { AgentFactory } from '@virtron/agency';
import { calculatorTool } from '@virtron/agency-tools';
import { webSearchTool } from '@virtron/agency-tools';

async function main() {
  // 1. Create and configure the AgentFactory
  const factory = new AgentFactory({
    apiKeys: {
      gemini: process.env.GEMINI_API_KEY,
    }
  });

  // Register the calculator tool with the AgentFactory
  factory.registerTool(calculatorTool);

  // 2. Define the configuration for the agent
  const agentConfig = {
    id: 'gemini-agent',
    name: 'Gemini Agent',
    description: 'An agent that uses the Gemini API.',
    provider: 'gemini',
    llmConfig: {
      model: 'gemini-2.5-flash-lite', 
    },
    role: 'A helpful assistant that can perform calculations using the calculator tool.',
    goals: ['Use the calculator tool when appropriate to perform mathematical calculations.'],
    tools: {
      calculator: 'calculator',
    },
  };

  // 3. Create the agent
  const agent = factory.createAgent(agentConfig);

  // 4. Run the agent
  const prompt = 'what is sqrt(64)?';
  console.log(`Running agent with prompt: "${prompt}"`);

  try {
    const response = await agent.run(prompt);
    console.log('Agent Response:');
    console.log(response);
  } catch (error) {
    console.error('An error occurred while running the agent:', error);
  }
}

main();
